import { cn } from "@/lib/cn";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

const buttonVariants = cva(
	"rounded-none active:translate-y-1 transition-transform relative inline-flex items-center justify-center gap-1.5",
	{
		variants: {
			font: {
				normal: "",
				retro: "retro",
			},
			variant: {
				default: "bg-foreground",
				destructive: "bg-destructive text-destructive-foreground",
				outline: "border-2 border-foreground bg-background",
				secondary: "bg-secondary text-secondary-foreground",
				ghost: "hover:bg-accent hover:text-accent-foreground",
				link: "text-primary underline-offset-4 hover:underline",
			},
			size: {
				default: "h-10 px-4 py-2 has-[>svg]:px-3",
				sm: "h-8 px-3 has-[>svg]:px-2.5",
				lg: "h-12 px-8 has-[>svg]:px-4",
				icon: "size-10",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
			font: "retro",
		},
	},
);

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>, VariantProps<typeof buttonVariants> {
	asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({ className, variant, size, font, asChild = false, ...props }, ref) => {
	const Comp = asChild ? Slot : "button";
const fullVariant = variant as NonNullable<VariantProps<typeof buttonVariants>['variant']>;

	const renderPixelBorder = () => {
		if (variant === "ghost" || variant === "link") return null;

		return (
			<>
				{/* Main border elements */}
				<div className="absolute -top-1.5 w-1/2 left-1.5 h-1.5 bg-foreground dark:bg-ring" />
				<div className="absolute -top-1.5 w-1/2 right-1.5 h-1.5 bg-foreground dark:bg-ring" />
				<div className="absolute -bottom-1.5 w-1/2 left-1.5 h-1.5 bg-foreground dark:bg-ring" />
				<div className="absolute -bottom-1.5 w-1/2 right-1.5 h-1.5 bg-foreground dark:bg-ring" />

				{/* Corner pixels */}
				<div className="absolute top-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
				<div className="absolute top-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
				<div className="absolute bottom-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
				<div className="absolute bottom-0 right-0 size-1.5 bg-foreground dark:bg-ring" />

				{/* Side pixels */}
				<div className="absolute top-1.5 -left-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
				<div className="absolute top-1.5 -right-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />

				{/* Shadows for non-outline variants */}
				{fullVariant == "outline" && fullVariant !== "ghost" && fullVariant !== "link" && (
					<>
						<div className="absolute top-0 left-0 w-full h-1.5 bg-foreground/20" />
						<div className="absolute top-1.5 left-0 w-3 h-1.5 bg-foreground/20" />
						<div className="absolute bottom-0 left-0 w-full h-1.5 bg-foreground/20" />
						<div className="absolute bottom-1.5 right-0 w-3 h-1.5 bg-foreground/20" />
					</>
				)}
			</>
		);
	};

	const renderIconBorder = () => (
		<>
			<div className="absolute top-0 left-0 w-full h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 w-full h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1 -left-1.5 w-1.5 h-1/2 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-1 -left-1.5 w-1.5 h-1/2 bg-foreground dark:bg-ring" />
			<div className="absolute top-1 -right-1.5 w-1.5 h-1/2 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-1 -right-1.5 w-1.5 h-1/2 bg-foreground dark:bg-ring" />
		</>
	);

	return (
		<Comp className={cn(buttonVariants({ variant, size, font, className }), "group")} ref={ref} {...props}>
			{asChild ? (
				<span className="relative inline-flex items-center justify-center gap-1.5">
					{props.children}
					{size === "icon" ? renderIconBorder() : renderPixelBorder()}
				</span>
			) : (
				<>
					{props.children}
					{size === "icon" ? renderIconBorder() : renderPixelBorder()}
				</>
			)}
		</Comp>
	);
});

Button.displayName = "Button";

export { Button };
